import React from 'react';
import AutoConfigDropdown from '../AutoConfigDropdown';
import { Form, Formik } from 'formik';
import { Button, Typography } from '@mui/material';
import LedgerTree from '../LedgerTree';

const data = [
  {
    parent_group_name: 'accounts_payable',
    ledgers: [
      {
        uuid_id: 'e1abced7-4a3c-519b-b7c4-fc11e2322146',
        ledger_name: 'Accounts Payable',
      },
    ],
  },
  {
    parent_group_name: 'Prepaid Expenses',
    ledgers: [
      {
        parent_ledger_name: 'Test Prepaid',
        ledgers: [
          {
            parent_ledger_name: null,
            ledgers: [
              {
                parent_ledger_name: 'test sub 2',
                ledgers: [
                  {
                    ledger_name: 'test sub 3',
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
];

function Ledgers() {
  const handleSubmit = (values) => {
    console.log('values ::: ', values);
  };

  const initialValues = {
    purchase: '',
    freight: '',
    insurance: '',
    tcs: '',
    round_off: '',
    discount: '',
    other_charges: '',
  };

  return (
    <div className="flex gap-4">
      <div className="w-1/3">
        <Formik initialValues={initialValues} onSubmit={handleSubmit}>
          {({ values, setFieldValue }) => (
            <Form className="flex flex-col gap-4 px-5">
              <Typography variant="h6" className="mb-4">
                Default Ledger
              </Typography>

              <AutoConfigDropdown
                label="Purchase"
                onSelect={(value) => setFieldValue('purchase', value?.uuid_id ?? '')}
                value={values.purchase}
                size="small"
              />

              <AutoConfigDropdown
                label="Freight"
                onSelect={(value) => setFieldValue('freight', value?.uuid_id ?? '')}
                value={values.freight}
              />

              <AutoConfigDropdown
                label="Insurance"
                onSelect={(value) => setFieldValue('insurance', value?.uuid_id ?? '')}
                value={values.insurance}
              />

              <AutoConfigDropdown
                label="TCS"
                onSelect={(value) => setFieldValue('tcs', value?.uuid_id ?? '')}
                value={values.tcs}
              />

              <AutoConfigDropdown
                label="Round off"
                onSelect={(value) => setFieldValue('round_off', value?.uuid_id ?? '')}
                value={values.round_off}
              />

              <AutoConfigDropdown
                label="Discount"
                onSelect={(value) => setFieldValue('discount', value?.uuid_id ?? '')}
                value={values.discount}
              />

              <AutoConfigDropdown
                label="Other Charges"
                onSelect={(value) => setFieldValue('other_charges', value?.uuid_id ?? '')}
                value={values.other_charges}
              />

              <Button variant="contained" color="primary" type="submit">
                Save
              </Button>
            </Form>
          )}
        </Formik>
      </div>
      <div className="flex-1">
        <LedgerTree data={data} />
      </div>
    </div>
  );
}

export default Ledgers;
