import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Switch, FormControlLabel, Button, IconButton } from '@mui/material';
import DownloadBtn from '../../../../../custom-components/DownloadBtn';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';

function TallyFields() {
  const [autoSyncMaster, setAutoSyncMaster] = useState(false);
  const [autoSyncInvoice, setAutoSyncInvoice] = useState(false);
  const [invoiceLevelPurchaseLedger, setInvoiceLevelPurchaseLedger] = useState(false);
  const [apiKey, setApiKey] = useState('243V-SDSJ-HHCBW-IDBKBFIE-RUI');
  const [copied, setCopied] = useState(false);

  const handleAutoSyncMasterChange = (event) => {
    const checked = event.target.checked;
    setAutoSyncMaster(checked);
    if (!checked) {
      setAutoSyncInvoice(false);
    }
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(apiKey);
    setCopied(true);
    setTimeout(() => setCopied(false), 1500);
  };

  const handleGenerateNewKey = () => {
    // Placeholder for API call to generate a new key
    const newKey = 'new-generated-api-key'; // Replace with actual API call
    setApiKey(newKey);
  };

  return (
    <div className="flex justify-evenly gap-6">
      <div className="flex justify-between min-w-[60%] gap-16 p-5 rounded-2xl shadow-lg bg-white border border-accent1-border">
        <div className="flex flex-col gap-6">
          <Typography variant="h6" className="font-semibold mb-4 text-primary-color border-b pb-2">
            Synchronization Settings
          </Typography>
          <div className="flex flex-col gap-4">
            <FormControlLabel
              control={<Switch checked={autoSyncMaster} onChange={handleAutoSyncMasterChange} color="primary" />}
              label="Auto Sync Master"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={autoSyncInvoice}
                  onChange={(event) => setAutoSyncInvoice(event.target.checked)}
                  disabled={!autoSyncMaster}
                  color="primary"
                />
              }
              label="Auto Sync Invoice"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={invoiceLevelPurchaseLedger}
                  onChange={(event) => setInvoiceLevelPurchaseLedger(event.target.checked)}
                  color="primary"
                />
              }
              label="Invoice level purchase ledger"
            />
          </div>
        </div>

        <div className="flex flex-col gap-6 flex-1">
          <Typography variant="h6" className="font-semibold mb-4 text-primary-color border-b pb-2">
            API Configuration
          </Typography>
          <div className="flex items-center gap-2">
            <Typography variant="body1" className="flex-grow p-2 border rounded-md bg-gray-50 font-mono">
              {apiKey}
            </Typography>
            <IconButton onClick={handleCopy} size="small">
              {copied ? <CheckIcon color="success" /> : <ContentCopyIcon />}
            </IconButton>
          </div>
          <Button variant="contained" className="!bg-accent2" onClick={handleGenerateNewKey}>
            Generate New Key
          </Button>
        </div>
      </div>

      <div className="flex flex-col justify-between gap-2 p-5 rounded-2xl shadow-lg bg-white border border-accent1-border">
        <Typography variant="h6" className="font-semibold mb-2 text-gray-800 border-b pb-2">
          Tally TDL and Instructions
        </Typography>

        <div className="flex flex-col justify-between gap-2">
          <p className="text-gray-600 text-lg">
            Download the Tally Definition Language (TDL) file and follow the included instructions to set up Tally
            integration with our system. If you need assistance with the setup process, please contact our support team.
          </p>

          <DownloadBtn
            downloadUrl="api/tally/download-tdl"
            className="!bg-accent2"
            onError={(err) => console.error('Download failed:', err)}
          />
        </div>
      </div>
    </div>
  );
}

export default TallyFields;
