import React, { useState } from 'react';
import DataTable from '../../../custom-components/DataTable';
import { useAuth } from '../../../../contexts/AuthContext';
import DownloadBtn from '../../../custom-components/DownloadBtn';
import UploadBtn from '../../../custom-components/UploadBtn';
import { useOutletContext } from 'react-router-dom';
import { Button, FormControlLabel, Radio, RadioGroup, Typography } from '@mui/material';
import AddItemForm from '../../ai-extraction/form-sections/components/AddItemForm';
import { autoConfigUrls } from '../../../utils/apiurls';

const COLUMNS = [
  {
    field: 'name',
    headerName: 'Item Name',
  },
  {
    field: 'description',
    headerName: 'Description',
  },
  {
    field: 'aliases',
    headerName: 'Aliases',
  },
  {
    field: 'hsn_sac',
    headerName: 'HSN/SAC Code',
  },
  {
    field: 'purchase_rate',
    headerName: 'Purchase Rate',
  },
  {
    field: 'unit',
    headerName: 'Unit',
  },
  {
    field: 'gst_rate',
    headerName: 'GST Rate',
  },
];

function Item() {
  const { globSelectedBusiness } = useAuth();
  const { businessPreferences, isZoho } = useOutletContext();
  const [open, setOpen] = useState(false);
  const [viewType, setViewType] = useState('items-list');

  // const isStockItem = viewType === 'stock-items';
  const url = `${autoConfigUrls.getStockItems}?business_id=${globSelectedBusiness?.business_id}`;
  const downloadUrl = `${autoConfigUrls.downloadStockItems}?business_id=${globSelectedBusiness?.business_id}`;
  const uploadUrl = `${autoConfigUrls.uploadStockItems}?business_id=${globSelectedBusiness?.business_id}`;

  return (
    <DataTable
      title={viewType === 'items-list' ? 'List of Items' : 'List of Items Aliases'}
      url={url}
      columns={COLUMNS}
    >
      <DataTable.BeforeSearch>
        <div className="flex items-center gap-2">
          <Typography variant="p">Select View Type: </Typography>
          <RadioGroup row name="view-type" value={viewType} onChange={(e) => setViewType(e.target.value)}>
            <FormControlLabel value="items-list" control={<Radio size="small" />} label="Items List" />
            <FormControlLabel value="items-aliases" control={<Radio size="small" />} label="Items Aliases" />
          </RadioGroup>
        </div>
      </DataTable.BeforeSearch>
      {!businessPreferences?.enable_auto_sync_master && (
        <>
          <DownloadBtn className="!rounded-md" downloadUrl={downloadUrl} />
          <UploadBtn className="!rounded-md" uploadUrl={uploadUrl} />
        </>
      )}
      {isZoho && (
        <>
          <Button variant="contained" className="!rounded-md" onClick={() => setOpen(true)}>
            Add Item
          </Button>
          <AddItemForm open={open} onClose={() => setOpen(false)} />
        </>
      )}
    </DataTable>
  );
}

export default Item;
