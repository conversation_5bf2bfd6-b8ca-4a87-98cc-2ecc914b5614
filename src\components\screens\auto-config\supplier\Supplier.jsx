import React, { useState } from 'react';
import DataTable from '../../../custom-components/DataTable';
import { useAuth } from '../../../../contexts/AuthContext';
import DownloadBtn from '../../../custom-components/DownloadBtn';
import UploadBtn from '../../../custom-components/UploadBtn';
import { useOutletContext } from 'react-router-dom';
import AddSupplierForm from '../../ai-extraction/form-sections/components/AddSupplierForm';
import { Button } from '@mui/material';

const COLUMNS = [
  {
    field: 'name',
    headerName: 'Supplier Name',
  },
  {
    field: 'gst_type',
    headerName: 'GST Type',
  },
  {
    field: 'gst_no',
    headerName: 'GST Number',
  },
  {
    field: 'state',
    headerName: 'State',
  },
  {
    field: 'state_address',
    headerName: 'State Address',
  },
];

function Supplier() {
  const { globSelectedBusiness } = useAuth();
  const { businessPreferences } = useOutletContext();
  const [open, setOpen] = useState(false);
  return (
    <DataTable
      title="List of Suppliers"
      url={`api/v1/ai_invoice/ledgers?business_id=${globSelectedBusiness?.business_id}`}
      columns={COLUMNS}
    >
      {!businessPreferences?.enable_auto_sync_master && (
        <>
          <DownloadBtn className="!rounded-md" />
          <UploadBtn className="!rounded-md" />
        </>
      )}
      {businessPreferences?.platform_details?.platform_name?.toLowerCase() === 'zoho' && (
        <>
          <Button variant="contained" className="!rounded-md" onClick={() => setOpen(true)}>
            Add Supplier
          </Button>
          <AddSupplierForm open={open} onClose={() => setOpen(false)} />
        </>
      )}
    </DataTable>
  );
}

export default Supplier;
