import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronRight } from '@mui/icons-material';

function LedgerNode({ node }) {
  const [expanded, setExpanded] = useState(false);

  const toggle = () => setExpanded(!expanded);

  const label = node.parent_group_name || node.parent_ledger_name || node.ledger_name;
  const hasChildren = node.ledgers && node.ledgers.length > 0;

  // If this is a node with null parent_ledger_name and has children, we'll render its children directly
  if (node.parent_ledger_name === null && hasChildren) {
    return (
      <div className="relative ml-5">
        {node.ledgers.map((child, idx) => (
          <LedgerNode key={idx} node={child} />
        ))}
      </div>
    );
  }

  return (
    <div className="relative ml-5">
      {/* Label and chevron icon */}
      <motion.div
        className={`flex items-center py-2 rounded-md transition-all duration-150 relative hover:bg-slate-50 ${
          hasChildren ? 'cursor-pointer' : ''
        }`}
        onClick={toggle}
      >
        <div className="flex items-center gap-2 w-full">
          {hasChildren ? (
            <motion.div
              className="flex items-center justify-center w-5 h-5 text-gray-500 transition-colors duration-150 hover:text-gray-700"
              variants={{
                collapsed: { rotate: 0 },
                expanded: { rotate: 90 },
              }}
              animate={expanded ? 'expanded' : 'collapsed'}
              transition={{ duration: 0.2, ease: 'easeInOut' }}
            >
              <ChevronRight fontSize="small" />
            </motion.div>
          ) : (
            <div className="flex items-center justify-center w-5 h-5">
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
            </div>
          )}
          <span className={`text-sm select-none text-gray-700 ${hasChildren ? 'font-semibold' : 'font-medium'}`}>
            {label}
          </span>
        </div>
      </motion.div>

      {/* Children nodes */}
      <AnimatePresence>
        {expanded && hasChildren && (
          <motion.div
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.05,
                  delayChildren: 0.1,
                },
              },
            }}
            initial="hidden"
            animate="visible"
            exit="hidden"
            className="overflow-hidden"
          >
            {node.ledgers.map((child, idx) => (
              <motion.div key={idx} initial="hidden" animate="visible" exit="hidden">
                <LedgerNode node={child} />
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default function LedgerTree({ data }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
    >
      {data.map((node, idx) => (
        <LedgerNode key={idx} node={node} />
      ))}
    </motion.div>
  );
}
